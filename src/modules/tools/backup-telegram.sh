#!/bin/bash

# Remnawave Backup System - Telegram Integration
# This module handles Telegram notifications and file uploads

# Function to escape special characters for MarkdownV2
escape_markdown_v2() {
    local text="$1"
    # Escape special characters for MarkdownV2
    echo "$text" | sed 's/[_*\[\]()~`>#+=|{}.!-]/\\&/g'
}

# Function to send a message to Telegram
send_telegram_message() {
    local message="$1"
    local parse_mode="${2:-MarkdownV2}"
    local escaped_message

    if [[ "$parse_mode" == "MarkdownV2" ]]; then
        escaped_message=$(escape_markdown_v2 "$message")
    else
        escaped_message="$message"
    fi

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        log_backup_operation "ERROR" "Telegram configuration missing"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
        -d "chat_id=$CHAT_ID" \
        -d "text=$escaped_message" \
        -d "parse_mode=$parse_mode" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" -eq 200 ]]; then
        return 0
    else
        log_backup_operation "ERROR" "Failed to send Telegram message. HTTP code: $http_code"
        return 1
    fi
}

# Function to send a document to Telegram
send_telegram_document() {
    local file_path="$1"
    local caption="$2"
    local parse_mode="${3:-MarkdownV2}"

    # Check if file exists
    if [[ ! -f "$file_path" ]]; then
        log_backup_operation "ERROR" "File not found for Telegram upload: $file_path"
        return 1
    fi

    # Escape caption for MarkdownV2 if needed
    local escaped_caption
    if [[ "$parse_mode" == "MarkdownV2" ]]; then
        escaped_caption=$(escape_markdown_v2 "$caption")
    else
        escaped_caption="$caption"
    fi

    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        log_backup_operation "ERROR" "Telegram configuration missing"
        return 1
    fi

    local http_code=$(curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendDocument" \
        -F "chat_id=$CHAT_ID" \
        -F "document=@$file_path" \
        -F "caption=$escaped_caption" \
        -F "parse_mode=$parse_mode" \
        -w "%{http_code}" -o /dev/null 2>&1)

    if [[ "$http_code" == "200" ]]; then
        return 0
    else
        log_backup_operation "ERROR" "Failed to send Telegram document. HTTP code: $http_code"
        return 1
    fi
}

# Function to send backup success notification
send_backup_success_notification() {
    local backup_path="$1"
    local backup_size="$2"
    local server_name="$3"
    local server_ip="$4"

    if [[ "$UPLOAD_METHOD" != "telegram" || -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        return 0
    fi

    local message="✅ *Remnawave Backup Created*

📊 *Backup Details:*
• File: \`$(basename "$backup_path")\`
• Size: \`$backup_size\`
• Server: \`$server_name\`
• IP: \`$server_ip\`
• Date: \`$(date '+%Y-%m-%d %H:%M:%S')\`

🔒 Backup completed successfully and is ready for download\\."

    if send_telegram_message "$message"; then
        log_backup_operation "INFO" "Backup success notification sent via Telegram"
        return 0
    else
        log_backup_operation "ERROR" "Failed to send backup success notification via Telegram"
        return 1
    fi
}

# Function to send backup failure notification
send_backup_failure_notification() {
    local error_message="$1"
    local server_name="$2"
    local server_ip="$3"

    if [[ "$UPLOAD_METHOD" != "telegram" || -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        return 0
    fi

    local message="❌ *Remnawave Backup Failed*

🚨 *Error Details:*
• Server: \`$server_name\`
• IP: \`$server_ip\`
• Date: \`$(date '+%Y-%m-%d %H:%M:%S')\`
• Error: \`$error_message\`

⚠️ Please check the server and backup configuration\\."

    if send_telegram_message "$message"; then
        log_backup_operation "INFO" "Backup failure notification sent via Telegram"
        return 0
    else
        log_backup_operation "ERROR" "Failed to send backup failure notification via Telegram"
        return 1
    fi
}

# Function to send backup with file upload
send_backup_via_telegram() {
    local backup_path="$1"
    
    if [[ "$UPLOAD_METHOD" != "telegram" || -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        log_backup_operation "INFO" "Telegram upload not configured, skipping"
        return 0
    fi

    if [[ ! -f "$backup_path" ]]; then
        log_backup_operation "ERROR" "Backup file not found for Telegram upload: $backup_path"
        return 1
    fi

    local server_name=$(hostname)
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
    local backup_size=$(du -h "$backup_path" 2>/dev/null | cut -f1 || echo "Unknown")
    
    local caption="Remnawave Backup
Server: $server_name
IP: $server_ip
Date: $(date '+%Y-%m-%d %H:%M:%S')
Size: $backup_size"

    if send_telegram_document "$backup_path" "$caption" "HTML"; then
        log_backup_operation "INFO" "Backup sent via Telegram successfully"
        return 0
    else
        log_backup_operation "ERROR" "Failed to send backup via Telegram"
        return 1
    fi
}

# Function to test Telegram configuration
test_telegram_config() {
    if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        show_error "Telegram configuration is not set"
        return 1
    fi

    show_info "Testing Telegram configuration..."

    local test_message="🧪 *Remnawave Backup System Test*

This is a test message to verify your Telegram configuration is working correctly\\.

✅ If you receive this message, your configuration is valid\\!"

    if send_telegram_message "$test_message"; then
        show_success "Telegram test message sent successfully"
        return 0
    else
        show_error "Failed to send Telegram test message"
        return 1
    fi
}

# Function to send cron setup notification
send_cron_setup_notification() {
    local cron_schedule="$1"
    
    if [[ "$UPLOAD_METHOD" != "telegram" || -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        return 0
    fi

    local server_name=$(hostname)
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")

    local message="⏰ *Remnawave Backup Schedule Updated*

📅 *Schedule Details:*
• Server: \`$server_name\`
• IP: \`$server_ip\`
• Schedule: \`$cron_schedule\`
• Date: \`$(date '+%Y-%m-%d %H:%M:%S')\`

🔄 Automatic backups are now configured and will be sent to this chat\\."

    if send_telegram_message "$message"; then
        log_backup_operation "INFO" "Cron setup notification sent via Telegram"
        return 0
    else
        log_backup_operation "ERROR" "Failed to send cron setup notification via Telegram"
        return 1
    fi
}

# Function to send restore completion notification
send_restore_notification() {
    local backup_file="$1"
    local restore_status="$2"  # "success" or "failed"
    
    if [[ "$UPLOAD_METHOD" != "telegram" || -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
        return 0
    fi

    local server_name=$(hostname)
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")

    local message
    if [[ "$restore_status" == "success" ]]; then
        message="✅ *Remnawave Restore Completed*

📦 *Restore Details:*
• Backup: \`$(basename "$backup_file")\`
• Server: \`$server_name\`
• IP: \`$server_ip\`
• Date: \`$(date '+%Y-%m-%d %H:%M:%S')\`

🔄 System has been successfully restored from backup\\."
    else
        message="❌ *Remnawave Restore Failed*

🚨 *Restore Details:*
• Backup: \`$(basename "$backup_file")\`
• Server: \`$server_name\`
• IP: \`$server_ip\`
• Date: \`$(date '+%Y-%m-%d %H:%M:%S')\`

⚠️ Restore operation failed\\. Please check the logs and try again\\."
    fi

    if send_telegram_message "$message"; then
        log_backup_operation "INFO" "Restore notification sent via Telegram"
        return 0
    else
        log_backup_operation "ERROR" "Failed to send restore notification via Telegram"
        return 1
    fi
}
