#!/bin/bash

# Remnawave Backup System - Core Functions
# This module contains core backup functionality shared between interactive and cron modes

# Constants
BACKUP_INSTALL_DIR="/opt/rw-backup-restore"
BACKUP_DIR="$BAC<PERSON>UP_INSTALL_DIR/backup"
BACKUP_CONFIG_FILE="$BAC<PERSON>UP_INSTALL_DIR/config.env"
BACKUP_SCRIPT_NAME="rw-backup.sh"
BACKUP_SCRIPT_PATH="$BACKUP_INSTALL_DIR/$BACKUP_SCRIPT_NAME"
BACKUP_LOG_FILE="$BACKUP_INSTALL_DIR/backup.log"
RETAIN_BACKUPS_DAYS=7
SYMLINK_PATH="/usr/local/bin/rw-backup"

# File paths for .env files
ENV_NODE_FILE=".env-node"
ENV_FILE=".env"
UPLOAD_METHOD="telegram"

# Backup validation constants
BACKUP_MAGIC_HEADER="REMNAWAVE_BACKUP_V1"
BACKUP_REQUIRED_FILES=("database.sql" "backup.info")
BACKUP_CHECKSUM_FILE="backup.sha256"

# Initialize variables that will be loaded from config
BOT_TOKEN=""
CHAT_ID=""
DB_USER="postgres"
GD_CLIENT_ID=""
GD_CLIENT_SECRET=""
GD_REFRESH_TOKEN=""
GD_FOLDER_ID=""
CRON_TIMES=""

# Session flag for dependency validation
BACKUP_DEPS_VALIDATED=""

# Logging function for backup operations
log_backup_operation() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')

    # Create log directory if it doesn't exist
    mkdir -p "$(dirname "$BACKUP_LOG_FILE")" 2>/dev/null

    # Write log entry
    echo "[$timestamp] [$level] $message" >> "$BACKUP_LOG_FILE"
}

# Universal error handling function
handle_backup_error() {
    local error_message="$1"
    local cleanup_function="${2:-}"
    local return_code="${3:-1}"

    show_error "$error_message"

    # Execute cleanup function if provided
    if [[ -n "$cleanup_function" && "$(type -t "$cleanup_function")" == "function" ]]; then
        "$cleanup_function"
    fi

    # Log error if logging is enabled
    log_backup_operation "ERROR" "$error_message"

    # Only prompt for enter in interactive mode
    if [[ -t 0 ]]; then
        prompt_for_enter
    fi
    return "$return_code"
}

# Function to cleanup temporary files
cleanup_temp_files() {
    local temp_dir="$1"
    if [[ -n "$temp_dir" && -d "$temp_dir" ]]; then
        rm -rf "$temp_dir"
        log_backup_operation "INFO" "Cleaned up temporary directory: $temp_dir"
    fi
}

# Function to create required directories
create_directories() {
    # Create backup installation directory
    if ! mkdir -p "$BACKUP_INSTALL_DIR" 2>/dev/null; then
        log_backup_operation "ERROR" "Failed to create backup installation directory: $BACKUP_INSTALL_DIR"
        return 1
    fi

    # Create backup storage directory
    if ! mkdir -p "$BACKUP_DIR" 2>/dev/null; then
        log_backup_operation "ERROR" "Failed to create backup storage directory: $BACKUP_DIR"
        return 1
    fi

    # Set appropriate permissions
    chmod 755 "$BACKUP_INSTALL_DIR" 2>/dev/null
    chmod 755 "$BACKUP_DIR" 2>/dev/null

    # Only change ownership if running as root and USER is defined
    if [[ $EUID -eq 0 && -n "$USER" && "$USER" != "root" ]]; then
        chown -R "$USER:$USER" "$BACKUP_INSTALL_DIR" 2>/dev/null || true
    fi

    log_backup_operation "INFO" "Created backup directories successfully"
    return 0
}

# Function to validate backup integrity
validate_backup_integrity() {
    local backup_path="$1"
    local validation_dir=""

    log_backup_operation "INFO" "Validating backup integrity: $(basename "$backup_path")"

    # Check if backup file exists
    if [[ ! -f "$backup_path" ]]; then
        log_backup_operation "ERROR" "Backup file does not exist: $backup_path"
        return 1
    fi

    # Check if backup file is not empty
    if [[ ! -s "$backup_path" ]]; then
        log_backup_operation "ERROR" "Backup file is empty: $backup_path"
        return 1
    fi

    # Test if archive can be extracted
    if ! tar -tzf "$backup_path" >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Backup archive is corrupted or invalid"
        return 1
    fi

    # Extract to temporary directory for validation
    validation_dir=$(mktemp -d)
    if ! tar -xzf "$backup_path" -C "$validation_dir" >/dev/null 2>&1; then
        cleanup_temp_files "$validation_dir"
        log_backup_operation "ERROR" "Failed to extract backup for validation"
        return 1
    fi

    # Check for required files
    local missing_files=()
    for required_file in "${BACKUP_REQUIRED_FILES[@]}"; do
        if [[ ! -f "$validation_dir/$required_file" ]]; then
            missing_files+=("$required_file")
        fi
    done

    if [[ ${#missing_files[@]} -gt 0 ]]; then
        cleanup_temp_files "$validation_dir"
        log_backup_operation "ERROR" "Backup is missing required files: ${missing_files[*]}"
        return 1
    fi

    # Check for Remnawave configuration files (at least one should exist)
    local remnawave_files_found=false
    for config_file in ".env" ".env-node" "docker-compose.yml"; do
        if [[ -f "$validation_dir/$config_file" ]]; then
            remnawave_files_found=true
            break
        fi
    done

    if [[ "$remnawave_files_found" == false ]]; then
        cleanup_temp_files "$validation_dir"
        log_backup_operation "ERROR" "Backup does not contain any Remnawave configuration files"
        return 1
    fi

    # Validate database dump
    if [[ -f "$validation_dir/database.sql" ]]; then
        if [[ ! -s "$validation_dir/database.sql" ]]; then
            cleanup_temp_files "$validation_dir"
            log_backup_operation "ERROR" "Database dump is empty"
            return 1
        fi
    fi

    # Validate checksum if present
    if [[ -f "$validation_dir/$BACKUP_CHECKSUM_FILE" ]]; then
        (
            cd "$validation_dir"
            if ! sha256sum -c "$BACKUP_CHECKSUM_FILE" >/dev/null 2>&1; then
                exit 1
            fi
        )
        
        if [[ $? -ne 0 ]]; then
            cleanup_temp_files "$validation_dir"
            log_backup_operation "ERROR" "Backup checksum verification failed"
            return 1
        fi
        log_backup_operation "INFO" "Backup checksum verified"
    fi

    cleanup_temp_files "$validation_dir"
    log_backup_operation "INFO" "Backup validation completed successfully"
    return 0
}

# Function to get database user from environment
get_database_user() {
    local db_user="$DB_USER"
    
    # Try to get database user from Remnawave .env file if not set in config
    if [[ "$db_user" == "postgres" && -f "$REMNAWAVE_DIR/.env" ]]; then
        local env_db_user=$(grep "^POSTGRES_USER=" "$REMNAWAVE_DIR/.env" | cut -d'=' -f2 | tr -d '"')
        if [[ -n "$env_db_user" ]]; then
            db_user="$env_db_user"
            log_backup_operation "INFO" "Using database user from .env: $db_user"
        fi
    fi
    
    echo "$db_user"
}

# Function to check Docker availability
check_docker_availability() {
    # Check if Docker command exists
    if ! command -v docker >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker command not found"
        return 1
    fi
    
    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker daemon is not running or not accessible"
        return 1
    fi
    
    # Check if remnawave-db container exists and is running
    if ! docker ps --format "{{.Names}}" | grep -q "^remnawave-db$"; then
        log_backup_operation "ERROR" "Remnawave database container is not running"
        return 1
    fi
    
    log_backup_operation "INFO" "Docker availability check passed"
    return 0
}

# Function to create backup metadata
create_backup_metadata() {
    local temp_dir="$1"
    
    echo "$BACKUP_MAGIC_HEADER" > "$temp_dir/backup.info"
    echo "Created: $(date '+%Y-%m-%d %H:%M:%S')" >> "$temp_dir/backup.info"
    echo "Server: $(hostname)" >> "$temp_dir/backup.info"
    echo "Version: ${VERSION:-unknown}" >> "$temp_dir/backup.info"
    
    log_backup_operation "INFO" "Created backup metadata"
}

# Function to generate checksums
generate_checksums() {
    local temp_dir="$1"
    local checksum_file="$temp_dir/$BACKUP_CHECKSUM_FILE"
    
    (
        cd "$temp_dir"
        find . -type f -not -name "$BACKUP_CHECKSUM_FILE" -exec sha256sum {} \; > "$checksum_file"
        cd - >/dev/null
    )
    
    log_backup_operation "INFO" "Generated checksums for backup validation"
}

# Function to clean up old backups
cleanup_old_backups() {
    local deleted_count=0
    
    # Find and delete old backup files
    while IFS= read -r -d '' file; do
        rm -f "$file"
        deleted_count=$((deleted_count + 1))
        log_backup_operation "INFO" "Deleted old backup: $(basename "$file")"
    done < <(find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -mtime +"$RETAIN_BACKUPS_DAYS" -print0 2>/dev/null)
    
    if [[ $deleted_count -gt 0 ]]; then
        log_backup_operation "INFO" "Cleaned up $deleted_count old backup(s)"
    fi
}
