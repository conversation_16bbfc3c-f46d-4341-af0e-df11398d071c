#!/bin/bash

# Remnawave Backup and Restore System - Main Interface
# This module provides the main menu and orchestrates backup functionality

# Function to get backup files array (newest to oldest)
get_backup_files() {
    local backups=()
    local i=0

    # Check if backup directory exists
    if [[ ! -d "$BACKUP_DIR" ]]; then
        return 1
    fi

    # Find and list all backup files (sorted newest to oldest)
    while IFS= read -r file; do
        backups[i++]="$file"
    done < <(find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -printf "%T@ %p\n" | sort -nr | cut -d' ' -f2-)

    # Return 1 if no backups found
    if [[ "${#backups[@]}" -eq 0 ]]; then
        return 1
    fi

    # Return the array of backup files
    echo "${backups[@]}"
}

# Function to display backup list with enhanced information
display_backup_list() {
    local backups=("$@")

    echo -e "${BOLD_GREEN}Available backups:${NC}"
    echo
    for i in "${!backups[@]}"; do
        local file="${backups[$i]}"
        local date=$(date -r "$file" "+%Y-%m-%d %H:%M:%S")
        local size=$(du -h "$file" | cut -f1)
        local filename=$(basename "$file")

        # All backups show as valid (no integrity check)
        local status_icon="✓"
        local status_color="$GREEN"

        # Display backup in single line format
        echo -e "${GREEN}$((i+1)).${NC} ${status_color}${status_icon}${NC} $filename ${ORANGE}[Date: $date | Size: $size]${NC}"
    done
}

# Function to list available backups with enhanced display (legacy compatibility)
list_backups() {
    local backup_files_output
    backup_files_output=$(get_backup_files)

    if [[ $? -ne 0 ]]; then
        return 1
    fi

    local backups=($backup_files_output)
    display_backup_list "${backups[@]}"

    # Return the array of backup files for compatibility
    echo "${backups[@]}"
}

# Function to show detailed backup information
show_backup_info() {
    local backup_path="$1"
    local temp_dir=$(mktemp -d)

    # Extract backup for inspection
    if ! tar -xzf "$backup_path" -C "$temp_dir" >/dev/null 2>&1; then
        show_error "Failed to extract backup for inspection"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    echo
    echo -e "${BOLD_GREEN}Backup Information:${NC}"
    echo -e "${ORANGE}File:${NC} $(basename "$backup_path")"
    echo -e "${ORANGE}Size:${NC} $(du -h "$backup_path" | cut -f1)"
    echo -e "${ORANGE}Created:${NC} $(date -r "$backup_path" "+%Y-%m-%d %H:%M:%S")"

    # Show backup metadata if available
    if [[ -f "$temp_dir/backup.info" ]]; then
        echo -e "${ORANGE}Metadata:${NC}"
        while IFS= read -r line; do
            echo "  $line"
        done < "$temp_dir/backup.info"
    fi

    # Show database size
    if [[ -f "$temp_dir/database.sql" ]]; then
        local db_size=$(du -h "$temp_dir/database.sql" | cut -f1)
        echo -e "${ORANGE}Database dump size:${NC} $db_size"
    fi

    # Show Remnawave files
    echo -e "${ORANGE}Remnawave files:${NC}"
    find "$temp_dir" -type f -not -name "database.sql" -not -name "backup.info" -not -name "backup.sha256" -exec basename {} \; | sed 's/^/  /'

    cleanup_temp_files "$temp_dir"
}

# Function to restore from a backup
restore_backup() {
    clear_screen
    draw_section_header "Restoring from backup"

    log_backup_operation "INFO" "Starting backup restoration"

    # Initialize backup system
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Get array of available backup files
    local backup_files_output
    backup_files_output=$(get_backup_files)

    if [[ $? -ne 0 ]]; then
        handle_backup_error "$(t backup_no_backups)"
        return 1
    fi

    # Convert to array properly
    local backups
    IFS=' ' read -ra backups <<< "$backup_files_output"
    local backup_count=${#backups[@]}

    # Display the numbered backup list
    display_backup_list "${backups[@]}"

    # Backup selection menu (numeric only)
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}Select backup number (1-$backup_count) or 0 to go back: ${NC}"
    read choice
    echo

    # Validate choice
    if ! [[ "$choice" =~ ^[0-9]+$ ]] || [ "$choice" -lt 0 ] || [ "$choice" -gt "$backup_count" ]; then
        handle_backup_error "$(t error_invalid_choice)"
        return 1
    fi

    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi

    # Get selected backup file
    local selected_backup="${backups[$((choice-1))]}"
    
    # Show backup information
    show_backup_info "$selected_backup"

    # Confirmation prompt
    echo
    echo -e "${BOLD_RED}$(t backup_restore_warning)${NC}"
    echo -e "${YELLOW}$(t backup_restore_warning_details)${NC}"

    echo ""
    if ! prompt_yes_no "Continue with restore?" "$BOLD_RED"; then
        log_backup_operation "INFO" "Backup restoration cancelled by user"
        return 0
    fi

    # Create temporary directory for extraction
    local temp_dir=$(mktemp -d)

    # Extract the backup archive
    show_info "Extracting backup..."
    if ! tar -xzf "$selected_backup" -C "$temp_dir" >/dev/null 2>&1; then
        handle_backup_error "Failed to extract backup archive" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Validate backup contents
    if [[ ! -f "$temp_dir/database.sql" ]]; then
        handle_backup_error "Invalid backup: Missing database dump" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Load configuration if exists
    load_config

    # Get database user
    local db_user=$(get_database_user)

    # Stop Remnawave services before restoration
    show_info "Stopping Remnawave services..."
    if [[ -f "$REMNAWAVE_DIR/docker-compose.yml" ]]; then
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" down >/dev/null 2>&1
    fi

    # Wait for services to stop
    sleep 3

    # Start only the database service for restoration
    show_info "Starting database service..."
    if [[ -f "$REMNAWAVE_DIR/docker-compose.yml" ]]; then
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d remnawave-db >/dev/null 2>&1
    fi

    # Wait for database to be ready
    local max_attempts=30
    local attempt=1
    while [[ $attempt -le $max_attempts ]]; do
        if docker exec remnawave-db pg_isready -U "$db_user" >/dev/null 2>&1; then
            break
        fi
        sleep 2
        attempt=$((attempt + 1))
    done

    if [ $attempt -eq $max_attempts ]; then
        handle_backup_error "Database failed to start within expected time" "cleanup_temp_files $temp_dir"
        # Try to restart services before returning
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
        return 1
    fi

    # Restore database with error handling
    show_info "Importing database dump..."
    if ! docker exec -i remnawave-db psql -U "$db_user" -d remnawave_db < "$temp_dir/database.sql" >/dev/null 2>&1; then
        handle_backup_error "Failed to restore database" "cleanup_temp_files $temp_dir"
        # Try to restart services before returning
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
        return 1
    fi

    # Restore all Remnawave files if they exist in the backup
    local has_remnawave_files=false
    for config_file in ".env" ".env-node" "docker-compose.yml"; do
        if [[ -f "$temp_dir/$config_file" ]]; then
            has_remnawave_files=true
            break
        fi
    done

    if [[ "$has_remnawave_files" == true ]]; then
        show_info "Restoring Remnawave files..."

        # Backup current Remnawave directory before restoration
        local backup_suffix=$(date +"%Y%m%d_%H%M%S")
        if [[ -d "$REMNAWAVE_DIR" ]]; then
            show_info "Creating backup of current Remnawave directory..."
            if ! cp -r "$REMNAWAVE_DIR" "${REMNAWAVE_DIR}.backup_${backup_suffix}" 2>/dev/null; then
                show_warning "Failed to backup current Remnawave directory"
            fi
        fi

        # Ensure Remnawave directory exists
        mkdir -p "$REMNAWAVE_DIR" 2>/dev/null

        # Restore all files from backup (excluding database.sql and backup metadata)
        for file in "$temp_dir"/*; do
            local filename=$(basename "$file")
            # Skip database dump and backup metadata files
            if [[ "$filename" != "database.sql" && "$filename" != "backup.info" && "$filename" != "$BACKUP_CHECKSUM_FILE" ]]; then
                if [[ -f "$file" ]]; then
                    if ! cp "$file" "$REMNAWAVE_DIR/" 2>/dev/null; then
                        show_warning "Failed to restore file: $filename"
                    fi
                elif [[ -d "$file" ]]; then
                    if ! cp -r "$file" "$REMNAWAVE_DIR/" 2>/dev/null; then
                        show_warning "Failed to restore directory: $filename"
                    fi
                fi
            fi
        done
        
        show_success "All Remnawave files restored successfully"
    else
        show_warning "No Remnawave configuration files found in backup"
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Restart all services
    show_info "Restarting Remnawave services..."
    if [[ -f "$REMNAWAVE_DIR/docker-compose.yml" ]]; then
        docker compose -f "$REMNAWAVE_DIR/docker-compose.yml" up -d >/dev/null 2>&1
    fi

    # Send restore notification
    send_restore_notification "$selected_backup" "success"

    show_success "$(t backup_restore_success)"
    log_backup_operation "INFO" "Backup restoration completed successfully"
    prompt_for_enter
    return 0
}

# Function to setup auto send configuration
setup_auto_send() {
    clear_screen
    draw_section_header "$(t backup_auto_send_title)"

    # Initialize backup
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Load or create configuration
    load_or_create_config
}

# Main backup and restore menu
backup_restore_menu() {
    while true; do
        clear_screen
        draw_section_header "$(t backup_menu_title)"

        echo -e "${GREEN}1.${NC} $(t backup_menu_create)"
        echo -e "${GREEN}2.${NC} $(t backup_menu_restore)"
        echo -e "${GREEN}3.${NC} $(t backup_menu_list)"
        echo -e "${GREEN}4.${NC} $(t backup_menu_auto_send)"
        echo -e "${GREEN}5.${NC} $(t backup_menu_cron)"
        echo -e "${GREEN}6.${NC} $(t backup_menu_test_telegram)"
        echo
        echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
        echo
        echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
        read choice
        echo

        case $choice in
            1)
                create_backup
                ;;
            2)
                restore_backup
                ;;
            3)
                clear_screen
                draw_section_header "$(t backup_menu_list)"
                
                if ! list_backups >/dev/null; then
                    show_error "$(t backup_no_backups)"
                else
                    list_backups >/dev/null
                fi
                
                prompt_for_enter
                ;;
            4)
                setup_auto_send
                ;;
            5)
                setup_cron
                ;;
            6)
                clear_screen
                draw_section_header "$(t backup_test_telegram_title)"
                
                # Load configuration
                if ! load_config; then
                    show_error "$(t backup_config_not_found)"
                    prompt_for_enter
                    continue
                fi
                
                test_telegram_config
                prompt_for_enter
                ;;
            0)
                break
                ;;
            *)
                show_error "$(t error_invalid_choice)"
                prompt_for_enter
                ;;
        esac
    done
}
