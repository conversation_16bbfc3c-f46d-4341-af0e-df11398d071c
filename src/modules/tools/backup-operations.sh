#!/bin/bash

# Remnawave Backup System - Core Operations
# This module contains the main backup and restore operations

# Function to create database dump
create_database_dump() {
    local temp_dir="$1"
    local db_dump="$temp_dir/database.sql"
    local db_user="$2"

    log_backup_operation "INFO" "Creating database dump with user: $db_user"

    # Export PostgreSQL database from Docker container
    if ! docker exec remnawave-db pg_dump -U "$db_user" -d remnawave_db > "$db_dump" 2>/dev/null; then
        log_backup_operation "ERROR" "Failed to export database. Check if database user '$db_user' exists"
        return 1
    fi

    # Verify database dump is not empty
    if [[ ! -s "$db_dump" ]]; then
        log_backup_operation "ERROR" "Database dump is empty"
        return 1
    fi

    log_backup_operation "INFO" "Database dump created successfully"
    return 0
}

# Function to copy Remnawave files
copy_remnawave_files() {
    local temp_dir="$1"
    local env_dir="$temp_dir/env"

    log_backup_operation "INFO" "Copying Remnawave files from: $REMNAWAVE_DIR"

    # Create directory for files
    if ! mkdir -p "$env_dir"; then
        log_backup_operation "ERROR" "Failed to create temporary environment directory"
        return 1
    fi

    # Check if Remnawave directory exists
    if [[ ! -d "$REMNAWAVE_DIR" ]]; then
        log_backup_operation "ERROR" "Remnawave directory not found: $REMNAWAVE_DIR"
        return 1
    fi

    # Copy entire Remnawave directory structure
    if ! cp -r "$REMNAWAVE_DIR"/* "$env_dir/" 2>/dev/null; then
        log_backup_operation "WARNING" "Failed to copy some Remnawave files"
        return 1
    fi

    log_backup_operation "INFO" "Successfully copied all Remnawave files"
    return 0
}

# Function to create backup archive
create_backup_archive() {
    local temp_dir="$1"
    local backup_path="$2"

    log_backup_operation "INFO" "Creating backup archive: $(basename "$backup_path")"

    # Create archive of all backup files
    if ! tar -czf "$backup_path" -C "$temp_dir" . >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Failed to create backup archive"
        return 1
    fi

    log_backup_operation "INFO" "Backup archive created successfully"
    return 0
}

# Function to create a backup (interactive mode)
create_backup() {
    clear_screen
    draw_section_header "Creating backup"

    log_backup_operation "INFO" "Starting backup creation"

    # Initialize backup system
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Load configuration if exists
    load_config

    # Get database user
    local db_user=$(get_database_user)
    show_info "Using database user from .env: $db_user"

    # Check Docker availability
    if ! check_docker_availability; then
        handle_backup_error "Docker is not available or Remnawave containers are not running"
        return 1
    fi

    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"

    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local env_dir="$temp_dir/env"
    local checksum_file="$temp_dir/$BACKUP_CHECKSUM_FILE"

    # Create database dump
    docker exec remnawave-db pg_dump -U "$db_user" -d remnawave_db > "$temp_dir/database.sql" 2>/dev/null &
    spinner $! "$(t backup_spinner_exporting_db)"
    wait $!

    if [[ $? -ne 0 ]] || [[ ! -s "$temp_dir/database.sql" ]]; then
        handle_backup_error "Failed to export database. Check if database user '$db_user' exists" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Copy Remnawave files
    (copy_remnawave_files "$temp_dir") &
    spinner $! "$(t backup_spinner_copying_files)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Failed to copy Remnawave files" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Create backup metadata
    create_backup_metadata "$temp_dir"

    # Generate checksums for validation
    (generate_checksums "$temp_dir") &
    spinner $! "$(t backup_spinner_generating_checksums)"
    wait $!

    # Create archive of all backup files
    (create_backup_archive "$temp_dir" "$backup_path") &
    spinner $! "$(t backup_spinner_creating_archive)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Failed to create backup archive" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Validate the created backup
    (validate_backup_integrity "$backup_path") &
    spinner $! "$(t backup_spinner_validating_integrity)"
    wait $!

    if [[ $? -ne 0 ]]; then
        handle_backup_error "Backup validation failed" "cleanup_temp_files $temp_dir"
        return 1
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Clean up old backups
    cleanup_old_backups

    # Get backup size for notifications
    local backup_size=$(du -h "$backup_path" 2>/dev/null | cut -f1 || echo "Unknown")
    local server_name=$(hostname)
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")

    # Send backup via Telegram if configured
    if [[ "$UPLOAD_METHOD" == "telegram" && -n "$BOT_TOKEN" && -n "$CHAT_ID" ]]; then
        (send_backup_via_telegram "$backup_path") &
        spinner $! "$(t backup_spinner_sending_telegram)"
        wait $!

        if [[ $? -eq 0 ]]; then
            show_success "Backup sent via Telegram"
            log_backup_operation "INFO" "Backup sent via Telegram successfully"
        else
            show_error "Failed to send backup via Telegram"
            log_backup_operation "ERROR" "Failed to send backup via Telegram"
            # Send failure notification
            send_backup_failure_notification "Failed to upload backup file" "$server_name" "$server_ip"
        fi
    elif [[ "$UPLOAD_METHOD" == "gdrive" && -n "$GD_CLIENT_ID" && -n "$GD_CLIENT_SECRET" ]]; then
        show_info "Google Drive upload not yet implemented"
        log_backup_operation "INFO" "Google Drive upload requested but not implemented"
    fi

    show_success "$(t backup_success): $backup_path"
    log_backup_operation "INFO" "Backup created successfully: $backup_path"
    prompt_for_enter
    return 0
}

# Function to create backup in silent mode (for cron)
create_backup_silent() {
    log_backup_operation "INFO" "=== Starting automated backup creation ==="
    log_backup_operation "INFO" "Script executed by: $(whoami)"
    log_backup_operation "INFO" "Current working directory: $(pwd)"

    # Initialize backup system
    if ! initialize_backup; then
        log_backup_operation "ERROR" "Failed to initialize backup system"
        return 1
    fi

    # Load configuration
    if ! load_config; then
        log_backup_operation "WARNING" "Configuration file not found or failed to load"
    fi

    # Get database user
    local db_user=$(get_database_user)

    # Check Docker availability with detailed logging
    log_backup_operation "INFO" "Checking Docker availability"
    if ! command -v docker >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker command not found"
        send_backup_failure_notification "Docker command not found" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        return 1
    fi

    if ! docker info >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker daemon is not running or not accessible"
        send_backup_failure_notification "Docker daemon not accessible" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        return 1
    fi

    if ! docker ps --format "{{.Names}}" | grep -q "^remnawave-db$"; then
        log_backup_operation "ERROR" "Remnawave database container is not running"
        send_backup_failure_notification "Database container not running" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        return 1
    fi

    log_backup_operation "INFO" "Docker availability check passed"

    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"

    # Create temporary directory
    local temp_dir=$(mktemp -d)

    # Create database dump
    if ! create_database_dump "$temp_dir" "$db_user"; then
        send_backup_failure_notification "Database dump failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Copy Remnawave files
    if ! copy_remnawave_files "$temp_dir"; then
        send_backup_failure_notification "File copy failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Create backup metadata
    create_backup_metadata "$temp_dir"

    # Generate checksums
    generate_checksums "$temp_dir"

    # Create archive
    if ! create_backup_archive "$temp_dir" "$backup_path"; then
        send_backup_failure_notification "Archive creation failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Validate backup
    if ! validate_backup_integrity "$backup_path"; then
        send_backup_failure_notification "Backup validation failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Clean up old backups
    cleanup_old_backups

    # Send backup via Telegram if configured
    local server_name=$(hostname)
    local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
    local backup_size=$(du -h "$backup_path" 2>/dev/null | cut -f1 || echo "Unknown")

    if [[ "$UPLOAD_METHOD" == "telegram" && -n "$BOT_TOKEN" && -n "$CHAT_ID" ]]; then
        if send_backup_via_telegram "$backup_path"; then
            log_backup_operation "INFO" "Backup sent via Telegram successfully"
        else
            log_backup_operation "ERROR" "Failed to send backup via Telegram"
            send_backup_failure_notification "Telegram upload failed" "$server_name" "$server_ip"
        fi
    fi

    log_backup_operation "INFO" "Backup created successfully: $backup_path"
    log_backup_operation "INFO" "Backup file size: $backup_size"
    log_backup_operation "INFO" "=== Automated backup creation completed ==="
    return 0
}
