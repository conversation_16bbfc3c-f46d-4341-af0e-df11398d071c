#!/bin/bash

# Remnawave Backup System - Cron Scheduling
# This module handles cron job setup and management

# Function to setup cron schedule
setup_cron() {
    clear_screen
    draw_section_header "$(t backup_cron_title)"

    # Initialize backup system
    if ! initialize_backup; then
        handle_backup_error "Failed to initialize backup system"
        return 1
    fi

    # Load configuration
    if ! load_config; then
        show_warning "$(t backup_config_not_found)"
        echo
        if ! prompt_yes_no "$(t backup_config_create_prompt)"; then
            return 0
        fi
        
        # Setup configuration first
        if ! load_or_create_config; then
            return 1
        fi
    fi

    # Validate Telegram configuration if using Telegram
    if [[ "$UPLOAD_METHOD" == "telegram" ]]; then
        if [[ -z "$BOT_TOKEN" || -z "$CHAT_ID" ]]; then
            show_error "$(t backup_telegram_not_configured)"
            echo
            if prompt_yes_no "$(t backup_telegram_configure_now)"; then
                if ! load_or_create_config; then
                    return 1
                fi
            else
                return 0
            fi
        fi
    fi

    echo -e "${BOL<PERSON>_GREEN}$(t backup_cron_schedule_options):${NC}"
    echo
    echo -e "${GREEN}1.${NC} $(t backup_cron_every_hour)"
    echo -e "${GREEN}2.${NC} $(t backup_cron_every_6_hours)"
    echo -e "${GREEN}3.${NC} $(t backup_cron_every_12_hours)"
    echo -e "${GREEN}4.${NC} $(t backup_cron_daily)"
    echo -e "${GREEN}5.${NC} $(t backup_cron_weekly)"
    echo -e "${GREEN}6.${NC} $(t backup_cron_custom)"
    echo -e "${GREEN}7.${NC} $(t backup_cron_disable)"
    echo
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
    read choice
    echo

    local cron_schedule=""
    local description=""

    case $choice in
        1)
            cron_schedule="0 * * * *"
            description="$(t backup_cron_every_hour)"
            ;;
        2)
            cron_schedule="0 */6 * * *"
            description="$(t backup_cron_every_6_hours)"
            ;;
        3)
            cron_schedule="0 */12 * * *"
            description="$(t backup_cron_every_12_hours)"
            ;;
        4)
            cron_schedule="0 2 * * *"
            description="$(t backup_cron_daily)"
            ;;
        5)
            cron_schedule="0 2 * * 0"
            description="$(t backup_cron_weekly)"
            ;;
        6)
            echo -e "$(t backup_cron_custom_format):"
            echo -e "${ORANGE}$(t backup_cron_format_example)${NC}"
            echo
            echo -ne "$(t backup_cron_enter_schedule): "
            read cron_schedule
            
            # Basic validation of cron format
            if [[ ! "$cron_schedule" =~ ^[0-9\*\/\-\,\ ]+$ ]] || [[ $(echo "$cron_schedule" | wc -w) -ne 5 ]]; then
                show_error "$(t backup_cron_invalid_format)"
                prompt_for_enter
                return 1
            fi
            
            description="$(t backup_cron_custom): $cron_schedule"
            ;;
        7)
            # Remove existing cron job
            if remove_cron_job; then
                show_success "$(t backup_cron_disabled)"
                log_backup_operation "INFO" "Backup cron job disabled"
            else
                show_error "$(t backup_cron_disable_failed)"
            fi
            prompt_for_enter
            return 0
            ;;
        0)
            return 0
            ;;
        *)
            show_error "$(t error_invalid_choice)"
            prompt_for_enter
            return 1
            ;;
    esac

    # Confirm the schedule
    echo -e "${BOLD_GREEN}$(t backup_cron_confirm_schedule):${NC}"
    echo -e "${ORANGE}$description${NC}"
    echo -e "${ORANGE}$(t backup_cron_schedule): $cron_schedule${NC}"
    echo

    if ! prompt_yes_no "$(t backup_cron_confirm_setup)"; then
        return 0
    fi

    # Save the cron schedule to config
    CRON_TIMES="$cron_schedule"
    save_config

    # Setup the cron job
    if setup_cron_job "$cron_schedule"; then
        show_success "$(t backup_cron_setup_success)"
        log_backup_operation "INFO" "Backup schedule set: $cron_schedule"
        
        # Send notification about cron setup
        send_cron_setup_notification "$cron_schedule"
        
        echo
        echo -e "${BOLD_GREEN}$(t backup_cron_next_info):${NC}"
        echo -e "${ORANGE}$(t backup_cron_log_location): $BACKUP_LOG_FILE${NC}"
        echo -e "${ORANGE}$(t backup_cron_manual_test): $BACKUP_SCRIPT_PATH --create --silent${NC}"
    else
        show_error "$(t backup_cron_setup_failed)"
        log_backup_operation "ERROR" "Failed to setup cron job"
    fi

    prompt_for_enter
}

# Function to setup cron job
setup_cron_job() {
    local schedule="$1"
    
    # Remove any existing backup cron jobs first
    remove_cron_job
    
    # Add new cron job
    local cron_command="$schedule $BACKUP_SCRIPT_PATH --create --silent"
    
    # Get current crontab, add new job, and install
    (crontab -l 2>/dev/null; echo "$cron_command") | crontab -
    
    # Verify the cron job was added
    if crontab -l 2>/dev/null | grep -q "$BACKUP_SCRIPT_PATH --create --silent"; then
        log_backup_operation "INFO" "Cron job added successfully: $cron_command"
        return 0
    else
        log_backup_operation "ERROR" "Failed to add cron job"
        return 1
    fi
}

# Function to remove existing backup cron jobs
remove_cron_job() {
    # Remove any lines containing the backup script path
    local temp_cron=$(mktemp)
    
    # Get current crontab and filter out backup-related jobs
    if crontab -l 2>/dev/null | grep -v "$BACKUP_SCRIPT_PATH" > "$temp_cron"; then
        # Install the filtered crontab
        crontab "$temp_cron"
        rm -f "$temp_cron"
        log_backup_operation "INFO" "Removed existing backup cron jobs"
        return 0
    else
        # No existing crontab or error occurred
        rm -f "$temp_cron"
        # Try to create empty crontab
        echo "" | crontab -
        log_backup_operation "INFO" "Cleared crontab (no existing jobs or error occurred)"
        return 0
    fi
}

# Function to check current cron status
check_cron_status() {
    echo -e "${BOLD_GREEN}$(t backup_cron_current_status):${NC}"
    echo
    
    # Check if cron service is running
    if systemctl is-active --quiet cron 2>/dev/null || systemctl is-active --quiet crond 2>/dev/null; then
        echo -e "${GREEN}✓${NC} $(t backup_cron_service_running)"
    else
        echo -e "${RED}✗${NC} $(t backup_cron_service_not_running)"
        return 1
    fi
    
    # Check for backup-related cron jobs
    local backup_jobs=$(crontab -l 2>/dev/null | grep "$BACKUP_SCRIPT_PATH" || true)
    
    if [[ -n "$backup_jobs" ]]; then
        echo -e "${GREEN}✓${NC} $(t backup_cron_job_configured)"
        echo -e "${ORANGE}$(t backup_cron_current_schedule):${NC}"
        echo "$backup_jobs" | sed 's/^/  /'
    else
        echo -e "${YELLOW}!${NC} $(t backup_cron_no_jobs)"
    fi
    
    # Check if backup script exists
    if [[ -f "$BACKUP_SCRIPT_PATH" ]]; then
        echo -e "${GREEN}✓${NC} $(t backup_cron_script_exists)"
        
        # Check if script is executable
        if [[ -x "$BACKUP_SCRIPT_PATH" ]]; then
            echo -e "${GREEN}✓${NC} $(t backup_cron_script_executable)"
        else
            echo -e "${RED}✗${NC} $(t backup_cron_script_not_executable)"
        fi
    else
        echo -e "${RED}✗${NC} $(t backup_cron_script_missing)"
    fi
    
    # Check recent backup activity
    if [[ -f "$BACKUP_LOG_FILE" ]]; then
        echo -e "${GREEN}✓${NC} $(t backup_cron_log_exists)"
        
        local recent_backups=$(grep "Backup created successfully" "$BACKUP_LOG_FILE" | tail -3)
        if [[ -n "$recent_backups" ]]; then
            echo -e "${ORANGE}$(t backup_cron_recent_backups):${NC}"
            echo "$recent_backups" | sed 's/^/  /'
        else
            echo -e "${YELLOW}!${NC} $(t backup_cron_no_recent_backups)"
        fi
    else
        echo -e "${YELLOW}!${NC} $(t backup_cron_no_log)"
    fi
}

# Function to test cron backup manually
test_cron_backup() {
    clear_screen
    draw_section_header "$(t backup_cron_test_title)"
    
    # Check if backup script exists
    if [[ ! -f "$BACKUP_SCRIPT_PATH" ]]; then
        show_error "$(t backup_cron_script_missing)"
        prompt_for_enter
        return 1
    fi
    
    # Check if script is executable
    if [[ ! -x "$BACKUP_SCRIPT_PATH" ]]; then
        show_error "$(t backup_cron_script_not_executable)"
        prompt_for_enter
        return 1
    fi
    
    show_info "$(t backup_cron_test_running)"
    echo -e "${ORANGE}$(t backup_cron_test_command): $BACKUP_SCRIPT_PATH --create --silent${NC}"
    echo
    
    # Run the backup script and capture output
    local start_time=$(date '+%Y-%m-%d %H:%M:%S')
    
    if "$BACKUP_SCRIPT_PATH" --create --silent; then
        show_success "$(t backup_cron_test_success)"
        
        # Show recent log entries
        if [[ -f "$BACKUP_LOG_FILE" ]]; then
            echo
            echo -e "${BOLD_GREEN}$(t backup_cron_test_log_output):${NC}"
            grep "\\[$start_time\\]\\|$(date '+%Y-%m-%d %H:%M')" "$BACKUP_LOG_FILE" | tail -10 | sed 's/^/  /'
        fi
    else
        show_error "$(t backup_cron_test_failed)"
        
        # Show error log entries
        if [[ -f "$BACKUP_LOG_FILE" ]]; then
            echo
            echo -e "${BOLD_RED}$(t backup_cron_test_error_log):${NC}"
            grep "ERROR\\|FAILED" "$BACKUP_LOG_FILE" | tail -5 | sed 's/^/  /'
        fi
    fi
    
    prompt_for_enter
}
