#!/bin/bash

# Remnawave Backup and Restore System - Refactored Main Module
# This module serves as the main entry point for the backup system

# The backup system has been refactored into multiple focused modules:
# - backup-core.sh: Core functions and constants
# - backup-config.sh: Configuration management and initialization  
# - backup-telegram.sh: Telegram integration
# - backup-operations.sh: Main backup and restore operations
# - backup-main.sh: User interface and menu functions
# - backup-cron.sh: Cron scheduling functionality

# All modules are included by the Makefile build process
# This file provides the main interface and backward compatibility

# Note: Main execution logic is handled by separate modules
# This module only provides functions for backup operations
