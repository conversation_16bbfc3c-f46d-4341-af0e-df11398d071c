#!/bin/bash

# Remnawave Backup System - Configuration Management
# This module handles loading, saving, and validating backup configuration

# Function to save configuration
save_config() {
    # Create the config directory if it doesn't exist
    mkdir -p "$(dirname "$BACKUP_CONFIG_FILE")" 2>/dev/null
    
    # Write config values to file
    cat > "$BACKUP_CONFIG_FILE" << EOF
BOT_TOKEN="$BOT_TOKEN"
CHAT_ID="$CHAT_ID"
DB_USER="$DB_USER"
UPLOAD_METHOD="$UPLOAD_METHOD"
GD_CLIENT_ID="$GD_CLIENT_ID"
GD_CLIENT_SECRET="$GD_CLIENT_SECRET"
GD_REFRESH_TOKEN="$GD_REFRESH_TOKEN"
GD_FOLDER_ID="$GD_FOLDER_ID"
CRON_TIMES="$CRON_TIMES"
RETAIN_BACKUPS_DAYS="$RETAIN_BACKUPS_DAYS"
EOF
    
    # Set appropriate permissions
    chmod 600 "$BACKUP_CONFIG_FILE"
    
    log_backup_operation "INFO" "Configuration saved successfully"
    return 0
}

# Function to load configuration
load_config() {
    if [[ -f "$BACKUP_CONFIG_FILE" ]]; then
        # Source the config file
        # shellcheck disable=SC1090
        source "$BACKUP_CONFIG_FILE"
        log_backup_operation "INFO" "Configuration loaded successfully"
        return 0
    else
        log_backup_operation "WARNING" "Configuration file not found: $BACKUP_CONFIG_FILE"
        return 1
    fi
}

# Function to validate Telegram configuration
validate_telegram_config() {
    local bot_token="$1"
    local chat_id="$2"

    # Check if bot token is provided and has correct format
    if [[ -z "$bot_token" ]]; then
        show_error "Telegram bot token is required"
        return 1
    fi

    # Basic bot token format validation (should be like 123456789:ABC-DEF...)
    if [[ ! "$bot_token" =~ ^[0-9]+:[A-Za-z0-9_-]+$ ]]; then
        show_error "Invalid Telegram bot token format"
        return 1
    fi

    # Check if chat ID is provided
    if [[ -z "$chat_id" ]]; then
        show_error "Telegram chat ID is required"
        return 1
    fi

    # Chat ID should be numeric (can be negative for groups)
    if [[ ! "$chat_id" =~ ^-?[0-9]+$ ]]; then
        show_error "Invalid Telegram chat ID format"
        return 1
    fi

    # Test connection to Telegram API
    show_info "Testing Telegram connection..."
    
    local test_response=$(curl -s -X POST "https://api.telegram.org/bot$bot_token/getMe" 2>/dev/null)

    if [[ -z "$test_response" ]]; then
        show_error "Failed to connect to Telegram API"
        return 1
    fi

    # Check if the response contains "ok":true
    if ! echo "$test_response" | grep -q '"ok":true'; then
        show_error "Invalid Telegram bot token"
        return 1
    fi

    # Test sending a message to the chat
    local test_message="Remnawave backup system configuration test"
    local send_response=$(curl -s -X POST "https://api.telegram.org/bot$bot_token/sendMessage" \
        -d "chat_id=$chat_id" \
        -d "text=$test_message" 2>/dev/null)

    if [[ -z "$send_response" ]] || ! echo "$send_response" | grep -q '"ok":true'; then
        show_error "Failed to send test message. Please check your chat ID"
        return 1
    fi

    show_success "Telegram configuration validated successfully"
    log_backup_operation "INFO" "Telegram configuration validated"
    return 0
}

# Function to validate Google Drive configuration
validate_gdrive_config() {
    local client_id="$1"
    local client_secret="$2"

    # Check if client ID is provided
    if [[ -z "$client_id" ]]; then
        show_error "Google Drive Client ID is required"
        return 1
    fi

    # Check if client secret is provided
    if [[ -z "$client_secret" ]]; then
        show_error "Google Drive Client Secret is required"
        return 1
    fi

    # Basic format validation for Google OAuth2 client ID
    if [[ ! "$client_id" =~ ^[0-9]+-[a-zA-Z0-9]+\.apps\.googleusercontent\.com$ ]]; then
        show_error "Invalid Google Drive Client ID format"
        return 1
    fi

    show_success "Google Drive configuration validated successfully"
    log_backup_operation "INFO" "Google Drive configuration validated"
    return 0
}

# Function to prompt for configuration settings
prompt_for_config() {
    clear_screen
    draw_section_header "$(t backup_config_method)"

    echo -e "${GREEN}1.${NC} $(t backup_config_telegram)"
    echo -e "${GREEN}2.${NC} $(t backup_config_google)"
    echo
    echo -e "${GREEN}0.${NC} $(t backup_menu_back)"
    echo
    echo -ne "${BOLD_BLUE_MENU}$(t main_menu_select_option) ${NC}"
    read choice
    echo

    # Validate choice
    if ! [[ "$choice" =~ ^[0-2]$ ]]; then
        handle_backup_error "$(t error_invalid_choice)"
        return 1
    fi

    # Handle back option
    if [[ "$choice" -eq 0 ]]; then
        return 0
    fi

    if [[ "$choice" == "1" ]]; then
        UPLOAD_METHOD="telegram"
        clear_screen

        draw_section_header "$(t backup_config_telegram)"

        echo -ne "$(t backup_config_telegram_bot): "
        read -r BOT_TOKEN

        echo -ne "$(t backup_config_telegram_id): "
        read -r CHAT_ID

        # Validate Telegram configuration
        if ! validate_telegram_config "$BOT_TOKEN" "$CHAT_ID"; then
            return 1
        fi

        # Show success message after successful validation
        show_success "$(t backup_config_saved)"
        prompt_for_enter
    else
        UPLOAD_METHOD="gdrive"
        clear_screen

        draw_section_header "$(t backup_config_google)"

        echo -e "Google Drive Client ID:"
        read -r GD_CLIENT_ID

        echo -e "Google Drive Client Secret:"
        read -r GD_CLIENT_SECRET

        echo -e "Google Drive Refresh Token (optional):"
        read -r GD_REFRESH_TOKEN

        echo -e "Google Drive Folder ID (optional):"
        read -r GD_FOLDER_ID

        # Validate Google Drive configuration
        if ! validate_gdrive_config "$GD_CLIENT_ID" "$GD_CLIENT_SECRET"; then
            return 1
        fi
    fi

    save_config
    log_backup_operation "INFO" "Configuration updated for method: $UPLOAD_METHOD"
}

# Function to load or create configuration
load_or_create_config() {
    if ! load_config; then
        prompt_for_config
    else
        prompt_for_config
    fi
}

# Function to initialize the backup system
initialize_backup() {
    log_backup_operation "INFO" "Initializing backup system"

    local actions_performed=false

    # Create required directories only if they don't exist
    if [[ ! -d "$BACKUP_INSTALL_DIR" ]] || [[ ! -d "$BACKUP_DIR" ]]; then
        if [[ -t 0 ]]; then
            spinner $$ "Creating directories..." &
            local spinner_pid=$!
        fi
        
        if ! create_directories; then
            if [[ -t 0 ]]; then
                kill $spinner_pid 2>/dev/null
                printf "\r\033[K" >/dev/tty
            fi
            return 1
        fi
        
        if [[ -t 0 ]]; then
            kill $spinner_pid 2>/dev/null
            printf "\r\033[K" >/dev/tty
            show_success "Directories created"
        fi
        actions_performed=true
    fi

    # Create backup script only if it doesn't exist
    if [[ ! -f "$BACKUP_SCRIPT_PATH" ]]; then
        if [[ -t 0 ]]; then
            spinner $$ "Creating backup script..." &
            local spinner_pid=$!
        fi
        
        create_backup_script
        
        if [[ -t 0 ]]; then
            kill $spinner_pid 2>/dev/null
            printf "\r\033[K" >/dev/tty
            show_success "Backup script created"
        fi
        actions_performed=true
    fi

    # Create symlink only if it doesn't exist or points to wrong target
    if [[ ! -L "$SYMLINK_PATH" ]] || [[ "$(readlink "$SYMLINK_PATH" 2>/dev/null)" != "$BACKUP_SCRIPT_PATH" ]]; then
        if [[ -t 0 ]]; then
            spinner $$ "Creating symlink..." &
            local spinner_pid=$!
        fi
        
        ln -sf "$BACKUP_SCRIPT_PATH" "$SYMLINK_PATH" 2>/dev/null || true
        
        if [[ -t 0 ]]; then
            kill $spinner_pid 2>/dev/null
            printf "\r\033[K" >/dev/tty
            show_success "Symlink created"
        fi
        actions_performed=true
    fi

    # Skip dependency validation if already verified in current session
    if [[ "$BACKUP_DEPS_VALIDATED" != "true" ]]; then
        # Ensure Docker is installed and running
        if ! command -v docker &>/dev/null; then
            handle_backup_error "Docker not installed. Please install Docker first."
            return 1
        fi

        # Check if required tools are available
        local missing_tools=()
        for cmd in curl gzip tar jq sha256sum; do
            if ! command -v $cmd &>/dev/null; then
                missing_tools+=("$cmd")
            fi
        done

        if [[ ${#missing_tools[@]} -gt 0 ]]; then
            handle_backup_error "Required commands not found: ${missing_tools[*]}. Please install them."
            return 1
        fi

        BACKUP_DEPS_VALIDATED="true"
        actions_performed=true
    fi

    if [[ "$actions_performed" == "true" && -t 0 ]]; then
        show_success "Backup system initialized successfully"
    fi

    log_backup_operation "INFO" "Backup system initialized successfully"
    return 0
}

# Function to create the standalone backup script for cron
create_backup_script() {
    cat > "$BACKUP_SCRIPT_PATH" << 'EOF'
#!/bin/bash

# Remnawave Backup Script - Standalone script for cron execution
# This script contains embedded backup functionality with POSIX compatibility

# Constants and variables
BACKUP_INSTALL_DIR="/opt/rw-backup-restore"
BACKUP_DIR="$BACKUP_INSTALL_DIR/backup"
BACKUP_CONFIG_FILE="$BACKUP_INSTALL_DIR/config.env"
BACKUP_LOG_FILE="$BACKUP_INSTALL_DIR/backup.log"
RETAIN_BACKUPS_DAYS=7
REMNAWAVE_DIR="/opt/remnawave"
ENV_NODE_FILE=".env-node"
ENV_FILE=".env"
UPLOAD_METHOD="telegram"
BACKUP_MAGIC_HEADER="REMNAWAVE_BACKUP_V1"
BACKUP_CHECKSUM_FILE="backup.sha256"

# Initialize variables
BOT_TOKEN=""
CHAT_ID=""
DB_USER="postgres"
CRON_TIMES=""

# Logging function
log_backup_operation() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" >> "$BACKUP_LOG_FILE"
}

# Load configuration
load_config() {
    if [ -f "$BACKUP_CONFIG_FILE" ]; then
        . "$BACKUP_CONFIG_FILE"
        return 0
    else
        return 1
    fi
}

# Cleanup function
cleanup_temp_files() {
    local temp_dir="$1"
    if [ -n "$temp_dir" ] && [ -d "$temp_dir" ]; then
        rm -rf "$temp_dir"
    fi
}

# Telegram sending function
send_telegram_document() {
    local file_path="$1"
    local caption="$2"

    if [ -z "$BOT_TOKEN" ] || [ -z "$CHAT_ID" ]; then
        return 1
    fi

    local api_url="https://api.telegram.org/bot${BOT_TOKEN}/sendDocument"

    curl -s -X POST "$api_url" \
        -F "chat_id=${CHAT_ID}" \
        -F "document=@${file_path}" \
        -F "caption=${caption}" \
        -F "parse_mode=HTML" >/dev/null 2>&1

    return $?
}

# Send failure notification
send_backup_failure_notification() {
    local error_message="$1"
    local server_name="$2"
    local server_ip="$3"

    if [ "$UPLOAD_METHOD" != "telegram" ] || [ -z "$BOT_TOKEN" ] || [ -z "$CHAT_ID" ]; then
        return 0
    fi

    local message="❌ Remnawave Backup Failed

🚨 Error Details:
• Server: $server_name
• IP: $server_ip
• Date: $(date '+%Y-%m-%d %H:%M:%S')
• Error: $error_message

⚠️ Please check the server and backup configuration."

    curl -s -X POST "https://api.telegram.org/bot$BOT_TOKEN/sendMessage" \
        -d "chat_id=$CHAT_ID" \
        -d "text=$message" \
        -d "parse_mode=HTML" >/dev/null 2>&1
}

# Main backup function for cron
create_backup_silent() {
    log_backup_operation "INFO" "=== Starting automated backup creation ==="
    log_backup_operation "INFO" "Script executed by: $(whoami)"
    log_backup_operation "INFO" "Current working directory: $(pwd)"
    log_backup_operation "INFO" "Script path: $0"
    log_backup_operation "INFO" "Arguments: $*"

    # Load configuration
    log_backup_operation "INFO" "Loading configuration from: $BACKUP_CONFIG_FILE"
    if load_config; then
        log_backup_operation "INFO" "Configuration loaded successfully"
        log_backup_operation "INFO" "BOT_TOKEN set: $([ -n "$BOT_TOKEN" ] && echo "YES" || echo "NO")"
        log_backup_operation "INFO" "CHAT_ID set: $([ -n "$CHAT_ID" ] && echo "YES" || echo "NO")"
        log_backup_operation "INFO" "DB_USER: $DB_USER"
        log_backup_operation "INFO" "UPLOAD_METHOD: $UPLOAD_METHOD"
    else
        log_backup_operation "WARNING" "Configuration file not found or failed to load"
    fi

    # Try to get database user from Remnawave .env file if not set in config
    if [ "$DB_USER" = "postgres" ] && [ -f "$REMNAWAVE_DIR/.env" ]; then
        local env_db_user=$(grep "^POSTGRES_USER=" "$REMNAWAVE_DIR/.env" | cut -d'=' -f2 | tr -d '"')
        if [ -n "$env_db_user" ]; then
            DB_USER="$env_db_user"
            log_backup_operation "INFO" "Using database user from .env: $DB_USER"
        fi
    fi

    # Generate timestamp for backup file
    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="remnawave_backup_${timestamp}.tar.gz"
    local backup_path="$BACKUP_DIR/$backup_file"

    # Create temporary directory
    local temp_dir=$(mktemp -d)
    local db_dump="$temp_dir/database.sql"
    local env_dir="$temp_dir/env"
    local checksum_file="$temp_dir/$BACKUP_CHECKSUM_FILE"

    # Create directory for env files
    if ! mkdir -p "$env_dir"; then
        log_backup_operation "ERROR" "Failed to create temporary environment directory"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Check Docker availability
    log_backup_operation "INFO" "Checking Docker availability"
    if ! command -v docker >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker command not found"
        send_backup_failure_notification "Docker command not found" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Check if Docker daemon is running
    if ! docker info >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Docker daemon is not running or not accessible"
        send_backup_failure_notification "Docker daemon not accessible" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # List all containers for debugging
    log_backup_operation "INFO" "Available containers:"
    docker ps -a --format "table {{.Names}}\t{{.Status}}" >> "$BACKUP_LOG_FILE" 2>&1

    # Check if remnawave-db container exists and is running
    if ! docker ps --format "{{.Names}}" | grep -q "^remnawave-db$"; then
        log_backup_operation "ERROR" "Remnawave database container is not running"
        log_backup_operation "INFO" "Checking if container exists but stopped..."
        if docker ps -a --format "{{.Names}}" | grep -q "^remnawave-db$"; then
            log_backup_operation "ERROR" "Container exists but is stopped"
        else
            log_backup_operation "ERROR" "Container does not exist"
        fi
        send_backup_failure_notification "Database container not running" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    log_backup_operation "INFO" "Database container is running"

    # Export PostgreSQL database from Docker container
    if ! docker exec remnawave-db pg_dump -U "$DB_USER" -d remnawave_db > "$db_dump" 2>/dev/null; then
        log_backup_operation "ERROR" "Failed to export database. Check if database user '$DB_USER' exists"
        send_backup_failure_notification "Database export failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Verify database dump is not empty
    if [ ! -s "$db_dump" ]; then
        log_backup_operation "ERROR" "Database dump is empty"
        send_backup_failure_notification "Database dump is empty" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Check and copy all Remnawave files
    log_backup_operation "INFO" "Checking Remnawave directory: $REMNAWAVE_DIR"
    if [ -d "$REMNAWAVE_DIR" ]; then
        log_backup_operation "INFO" "Remnawave directory exists, listing contents:"
        ls -la "$REMNAWAVE_DIR" >> "$BACKUP_LOG_FILE" 2>&1

        log_backup_operation "INFO" "Copying all Remnawave files to backup"
        if ! cp -r "$REMNAWAVE_DIR"/* "$env_dir/" 2>>"$BACKUP_LOG_FILE"; then
            log_backup_operation "WARNING" "Failed to copy some Remnawave files"
        else
            log_backup_operation "INFO" "Successfully copied all Remnawave files"
            log_backup_operation "INFO" "Backup directory contents:"
            ls -la "$env_dir" >> "$BACKUP_LOG_FILE" 2>&1
        fi
    else
        log_backup_operation "ERROR" "Remnawave directory not found: $REMNAWAVE_DIR"
        log_backup_operation "INFO" "Checking parent directory:"
        ls -la "$(dirname "$REMNAWAVE_DIR")" >> "$BACKUP_LOG_FILE" 2>&1
        send_backup_failure_notification "Remnawave directory not found" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Create backup metadata
    echo "$BACKUP_MAGIC_HEADER" > "$temp_dir/backup.info"
    echo "Created: $(date '+%Y-%m-%d %H:%M:%S')" >> "$temp_dir/backup.info"
    echo "Server: $(hostname)" >> "$temp_dir/backup.info"

    # Generate checksums
    cd "$temp_dir"
    find . -type f -not -name "$BACKUP_CHECKSUM_FILE" -exec sha256sum {} \; > "$checksum_file"
    cd - >/dev/null

    # Create archive
    if ! tar -czf "$backup_path" -C "$temp_dir" . >/dev/null 2>&1; then
        log_backup_operation "ERROR" "Failed to create backup archive"
        send_backup_failure_notification "Archive creation failed" "$(hostname)" "$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")"
        cleanup_temp_files "$temp_dir"
        return 1
    fi

    # Clean up temporary directory
    cleanup_temp_files "$temp_dir"

    # Clean up old backups
    find "$BACKUP_DIR" -name "remnawave_backup_*.tar.gz" -type f -mtime +"$RETAIN_BACKUPS_DAYS" -delete 2>/dev/null

    # Send backup via Telegram if configured
    if [ "$UPLOAD_METHOD" = "telegram" ] && [ -n "$BOT_TOKEN" ] && [ -n "$CHAT_ID" ]; then
        local server_name=$(hostname)
        local server_ip=$(curl -s ifconfig.me 2>/dev/null || echo "Unknown")
        local caption="Remnawave Backup
Server: $server_name
IP: $server_ip
Date: $(date '+%Y-%m-%d %H:%M:%S')"

        if send_telegram_document "$backup_path" "$caption"; then
            log_backup_operation "INFO" "Backup sent via Telegram successfully"
        else
            log_backup_operation "ERROR" "Failed to send backup via Telegram"
            send_backup_failure_notification "Telegram upload failed" "$server_name" "$server_ip"
        fi
    fi

    log_backup_operation "INFO" "Backup created successfully: $backup_path"
    log_backup_operation "INFO" "Backup file size: $(du -h "$backup_path" 2>/dev/null | cut -f1 || echo "unknown")"
    log_backup_operation "INFO" "=== Automated backup creation completed ==="
    return 0
}

# Parse command line arguments
BACKUP_CREATE_MODE=false
BACKUP_SILENT_MODE=false

while [ $# -gt 0 ]; do
    case $1 in
        --create)
            BACKUP_CREATE_MODE=true
            shift
            ;;
        --silent)
            BACKUP_SILENT_MODE=true
            shift
            ;;
        --help|-h)
            echo "Usage: $0 [--create] [--silent]"
            echo "  --create    Create a backup"
            echo "  --silent    Run in silent mode (for cron)"
            exit 0
            ;;
        *)
            echo "Unknown option: $1" >&2
            exit 1
            ;;
    esac
done

# Execute backup
if [ "$BACKUP_CREATE_MODE" = "true" ]; then
    if [ "$BACKUP_SILENT_MODE" = "true" ]; then
        # Ensure log file exists and is writable
        mkdir -p "$(dirname "$BACKUP_LOG_FILE")" 2>/dev/null
        touch "$BACKUP_LOG_FILE" 2>/dev/null

        # Log script start
        echo "=== CRON BACKUP SCRIPT START ===" >> "$BACKUP_LOG_FILE"
        echo "Date: $(date)" >> "$BACKUP_LOG_FILE"
        echo "User: $(whoami)" >> "$BACKUP_LOG_FILE"
        echo "Environment:" >> "$BACKUP_LOG_FILE"
        env | grep -E "(PATH|HOME|USER|SHELL)" >> "$BACKUP_LOG_FILE" 2>&1
        echo "====================================" >> "$BACKUP_LOG_FILE"

        # Execute backup with error handling
        if create_backup_silent; then
            echo "=== CRON BACKUP SCRIPT SUCCESS ===" >> "$BACKUP_LOG_FILE"
            exit 0
        else
            echo "=== CRON BACKUP SCRIPT FAILED ===" >> "$BACKUP_LOG_FILE"
            exit 1
        fi
    else
        echo "Interactive mode not supported in standalone script"
        exit 1
    fi
else
    echo "Usage: $0 --create --silent"
    echo "This is a standalone backup script for cron execution"
    exit 1
fi
EOF

    # Make the script executable
    chmod +x "$BACKUP_SCRIPT_PATH"
    log_backup_operation "INFO" "Standalone backup script created: $BACKUP_SCRIPT_PATH"
}
